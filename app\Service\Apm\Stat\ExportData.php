<?php

/**
 * 性能趋势统计
 * @desc 性能趋势统计
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2024/02/18
 */

namespace App\Service\Apm\Stat;

use App\Model\Apm\StarRocks\PerformanceStatData;

class ExportData extends BaseStat
{
    /**
     * 获取统计数据
     *
     * @param string $groupType 分组类型：day(按天), week(按周), month(按月)
     * @return array 返回按指定时间维度分组的性能统计数据
     * @throws \InvalidArgumentException 当分组类型不支持时抛出异常
     */
    public function getData($groupType = 'day'): array
    {
        // 验证分组类型的有效性
        $this->validateGroupType($groupType);

        return $this->getStatData($groupType);
    }

    /**
     * 导出数据的标题
     *
     * @return array
     */
    public function exportTitle()
    {
        return [
            '时间',
            '性能评分',
            'FPS均值（帧/秒）',
            'Stutter卡顿率（%）',
            'PSS内存峰值均值（MB）',
            'FPS抖动（次/10分钟）',
            'BigJank卡顿（次/10分钟）',
            'Lua_Memory均值（MB）',
            'Mono_Memory均值（MB）',
            'TotalCPU使用峰值均值（%）',
            'FpsPower均值（mw/帧）'
        ];
    }

    /**
     * 获取性能统计数据
     *
     * @param string $groupType 分组类型：day(按天), week(按周), month(按月)
     * @return array 返回性能统计数据数组
     */
    protected function getStatData($groupType = 'day'): array
    {
        // 根据分组类型生成对应的日期选择和分组SQL语句
        $dateSelectAndGroup = $this->getDateSelectAndGroup($groupType);

        // 构建性能指标统计的SQL查询语句
        $selectRaw = $this->buildPerformanceSelectSql($dateSelectAndGroup['select']);

        // 执行查询并返回结果
        return $this->executePerformanceQuery($selectRaw, $dateSelectAndGroup['group']);
    }

    /**
     * 构建性能指标统计的SQL查询语句
     *
     * @param string $dateSelect 日期选择语句
     * @return string 完整的SELECT语句
     */
    private function buildPerformanceSelectSql($dateSelect): string
    {
        return <<<SQL
{$dateSelect} as date,
round(sum({$this->performance_score_data_table}.all_score) / count({$this->performance_score_data_table}.session_id), 2) as score,
round(sum({$this->performance_stat_data_table}.sum_fps) / sum({$this->performance_stat_data_table}.num), 2) as fps,
round((sum({$this->performance_stat_data_table}.sum_jank_time / {$this->performance_stat_data_table}.sum_frame_times_time) / count({$this->performance_stat_data_table}.session_id)) * 100, 2) as stutter,
round((sum({$this->performance_stat_data_table}.sum_used_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as memory,
round(sum({$this->performance_stat_data_table}.fps_jitter_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as fps_jitter,
round(sum({$this->performance_stat_data_table}.big_jank_count_10) / count({$this->performance_stat_data_table}.session_id), 2) as big_jank,
round((sum({$this->performance_stat_data_table}.sum_lua_memory) / sum({$this->performance_stat_data_table}.num)) / (1024*1024), 2) as lua_memory,
round((sum({$this->performance_stat_data_table}.sum_mono_used_size) / COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_mono_used_size > 0 THEN 1 END)) / (1024*1024), 2) as mono_used_size,
round((sum({$this->performance_stat_data_table}.sum_cpu_total_usage) / sum({$this->performance_stat_data_table}.num)), 2) as cpu_total,
round((sum({$this->performance_stat_data_table}.sum_battery_power) / sum({$this->performance_stat_data_table}.sum_fps)) / COUNT(CASE WHEN {$this->performance_stat_data_table}.sum_battery_power > 0 THEN 1 END), 2) as fps_power
SQL;
    }

    /**
     * 执行性能数据查询
     *
     * @param string $selectRaw SELECT语句
     * @param string $groupBy GROUP BY语句
     * @return array 查询结果
     */
    private function executePerformanceQuery($selectRaw, $groupBy): array
    {
        return PerformanceStatData::query()
            ->selectRaw($selectRaw)
            ->join($this->mysql_apm_report_list_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->mysql_apm_report_list_table}.id") // 连接报告表
            ->join($this->mysql_apm_device_list_table, function ($join) { // 连接设备表
                $join->on("{$this->mysql_apm_report_list_table}.developer_app_id", '=', "{$this->mysql_apm_device_list_table}.developer_app_id")
                    ->on("{$this->mysql_apm_report_list_table}.dev_str", '=', "{$this->mysql_apm_device_list_table}.dev_str");
            })
            ->join($this->performance_score_data_table, "{$this->performance_stat_data_table}.session_id", '=', "{$this->performance_score_data_table}.session_id") // 连接分数表
            ->where("{$this->performance_stat_data_table}.duration", '>', $this->getMinDuration()) // 过滤小于最小耗时的数据
            ->whereBetween("{$this->mysql_apm_report_list_table}.created_at", [$this->startTime, $this->endTime]) // 时间范围过滤
            ->where("{$this->mysql_apm_report_list_table}.developer_app_id", $this->params['developer_app_id']) // 应用ID过滤
            ->when(isset($this->params['os_type']), function ($query) { // 操作系统类型过滤
                return $query->where("{$this->mysql_apm_device_list_table}.os_type", $this->params['os_type']);
            })
            ->when(isset($this->params['is_simulator']) && is_numeric($this->params['is_simulator']), function ($query) { // 模拟器过滤
                return $query->where("{$this->mysql_apm_device_list_table}.is_simulator", $this->params['is_simulator']);
            })
            ->when(isset($this->params['device_tier']) && is_numeric($this->params['device_tier']), function ($query) { // 设备挡位过滤
                return $query->where("{$this->mysql_apm_device_list_table}.device_tier", $this->params['device_tier']);
            })
            ->when(isset($this->params['game_version_code']), function ($query) { // 游戏版本过滤
                return $query->where("{$this->mysql_apm_report_list_table}.app_version_name", $this->params['game_version_code']);
            })
            ->when($this->params['inner_version'] ?? null, function ($query) { // 内部版本过滤
                return $query->where("{$this->mysql_apm_report_list_table}.inner_version", $this->params['inner_version']);
            })
            ->when($this->params['quality'] ?? null, function ($query) { // 画质设置过滤
                return $query->where("{$this->mysql_apm_report_list_table}.quality", $this->params['quality']);
            })
            ->where("{$this->performance_stat_data_table}.jank_count_10", '<=', 2000) // 过滤异常卡顿数据
            ->groupByRaw($groupBy) // 按指定维度分组
            ->getFromSR(); // 从StarRocks获取数据
    }

    /**
     * 验证分组类型的有效性
     *
     * @param string $groupType 分组类型
     * @throws \InvalidArgumentException 当分组类型无效时抛出异常
     */
    private function validateGroupType($groupType): void
    {
        $validTypes = ['day', 'week', 'month'];

        if (!in_array($groupType, $validTypes)) {
            throw new \InvalidArgumentException("不支持的分组类型: {$groupType}。支持的类型: " . implode(', ', $validTypes));
        }
    }

    /**
     * 根据分组类型生成日期选择和分组语句
     *
     * @param string $groupType 分组类型：day(按天), week(按周), month(按月)
     * @return array 包含select和group键的数组
     */
    protected function getDateSelectAndGroup($groupType): array
    {
        $table = $this->mysql_apm_report_list_table;

        switch ($groupType) {
            case 'day':
                // 按天分组：返回格式 2025-04-01
                return [
                    'select' => "DATE_FORMAT({$table}.created_at, '%Y-%m-%d')",
                    'group' => "DATE_FORMAT({$table}.created_at, '%Y-%m-%d')"
                ];

            case 'week':
                // 按周分组：返回格式 2025-04-01-2025-04-07 (周一到周日)
                return [
                    'select' => "CONCAT(
                        DATE_FORMAT(DATE_SUB({$table}.created_at, INTERVAL WEEKDAY({$table}.created_at) DAY), '%Y-%m-%d'),
                        '-',
                        DATE_FORMAT(DATE_ADD(DATE_SUB({$table}.created_at, INTERVAL WEEKDAY({$table}.created_at) DAY), INTERVAL 6 DAY), '%Y-%m-%d')
                    )",
                    'group' => "YEAR({$table}.created_at), WEEK({$table}.created_at, 1)"
                ];

            case 'month':
                // 按月分组：返回格式 2025-04
                return [
                    'select' => "DATE_FORMAT({$table}.created_at, '%Y-%m')",
                    'group' => "DATE_FORMAT({$table}.created_at, '%Y-%m')"
                ];

            default:
                // 默认按天分组
                return [
                    'select' => "DATE_FORMAT({$table}.created_at, '%Y-%m-%d')",
                    'group' => "DATE_FORMAT({$table}.created_at, '%Y-%m-%d')"
                ];
        }
    }
}

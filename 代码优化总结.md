# ExportData 类代码优化总结

## 优化内容

### 1. 移除小时分组
- 移除了 `hour` 分组类型，只保留 `day`、`week`、`month` 三种分组
- 默认分组类型改为 `day`（按天分组）

### 2. 代码结构优化
- **方法拆分**：将原来的大方法拆分为多个职责单一的小方法
- **可读性提升**：增加了详细的中文注释，说明每个方法的作用
- **参数验证**：添加了 `validateGroupType()` 方法验证输入参数

### 3. 新增的方法

#### `validateGroupType($groupType)`
- **作用**：验证分组类型的有效性
- **参数**：分组类型字符串
- **异常**：当分组类型无效时抛出 `InvalidArgumentException`

#### `buildPerformanceSelectSql($dateSelect)`
- **作用**：构建性能指标统计的SQL查询语句
- **参数**：日期选择语句
- **返回**：完整的SELECT语句字符串

#### `executePerformanceQuery($selectRaw, $groupBy)`
- **作用**：执行性能数据查询
- **参数**：SELECT语句和GROUP BY语句
- **返回**：查询结果数组

### 4. 注释优化
- **方法注释**：每个方法都有详细的PHPDoc注释
- **行内注释**：关键代码行添加了中文注释说明
- **SQL注释**：数据库查询的每个条件都有对应的中文说明

### 5. 代码示例

#### 优化前（原始代码）
```php
protected function getStatData($groupType = 'hour'): array
{
    $selectRaw = <<<COLUMNS
DATE_FORMAT({$this->mysql_apm_report_list_table}.created_at, '%Y-%m-%d %H:00:00') as date,
// ... 大量SQL代码混在一起
COLUMNS;

    return PerformanceStatData::query()
        // ... 大量查询条件混在一起
        ->getFromSR();
}
```

#### 优化后（新代码）
```php
protected function getStatData($groupType = 'day'): array
{
    // 根据分组类型生成对应的日期选择和分组SQL语句
    $dateSelectAndGroup = $this->getDateSelectAndGroup($groupType);

    // 构建性能指标统计的SQL查询语句
    $selectRaw = $this->buildPerformanceSelectSql($dateSelectAndGroup['select']);

    // 执行查询并返回结果
    return $this->executePerformanceQuery($selectRaw, $dateSelectAndGroup['group']);
}
```

## 优化效果

### 1. 可维护性提升
- **单一职责**：每个方法只负责一个特定功能
- **易于修改**：修改SQL逻辑时只需要修改对应的小方法
- **易于测试**：可以单独测试每个方法的功能

### 2. 可读性提升
- **清晰的方法名**：方法名直接说明了功能
- **详细的注释**：中文注释让代码更容易理解
- **逻辑分层**：主方法只关注流程，具体实现在子方法中

### 3. 错误处理改进
- **参数验证**：在方法入口就验证参数有效性
- **明确的异常**：抛出具体的异常类型和错误信息

### 4. 扩展性提升
- **易于添加新分组**：只需要在 `getDateSelectAndGroup()` 方法中添加新的case
- **易于修改SQL**：SQL构建逻辑独立在单独的方法中

## 使用方式

```php
// 创建实例
$exportData = new ExportData($params);

// 按天分组（默认）
$dailyData = $exportData->getData();
$dailyData = $exportData->getData('day');

// 按周分组
$weeklyData = $exportData->getData('week');

// 按月分组
$monthlyData = $exportData->getData('month');

// 错误处理
try {
    $data = $exportData->getData('invalid_type');
} catch (InvalidArgumentException $e) {
    echo $e->getMessage(); // 输出：不支持的分组类型: invalid_type。支持的类型: day, week, month
}
```

## 时间格式
- **按天**：`2025-04-01`
- **按周**：`2025-04-01-2025-04-07`（周一到周日）
- **按月**：`2025-04`

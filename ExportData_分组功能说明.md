# ExportData 分组功能说明

## 概述

`ExportData` 类现在支持按不同时间维度进行数据分组统计，包括按小时、按天、按周、按月四种分组方式。

## 功能特性

### 支持的分组类型

1. **按天分组** (`day`) - 默认
2. **按周分组** (`week`)
3. **按月分组** (`month`)

### 时间格式说明

| 分组类型 | 返回格式 | 示例 | 说明 |
|---------|---------|------|------|
| day | `YYYY-MM-DD` | `2024-01-15` | 按天分组，显示具体日期 |
| week | `YYYY-MM-DD-YYYY-MM-DD` | `2024-01-15-2024-01-21` | 按周分组，显示周一到周日的日期范围 |
| month | `YYYY-MM` | `2024-01` | 按月分组，显示年月 |

## 使用方法

### 基本调用

```php
use App\Service\Apm\Stat\ExportData;

// 初始化参数
$params = [
    'start_time' => '2024-01-01',
    'end_time' => '2024-01-31',
    'developer_app_id' => 123,
    'os_type' => 1, // 可选：平台类型
    'game_version_code' => 'v1.0.0', // 可选：游戏版本
    // ... 其他可选参数
];

// 创建实例
$exportData = new ExportData($params);
```

### 不同分组方式调用

```php
// 1. 按天分组（默认）
$dailyData = $exportData->getData();
// 或者明确指定
$dailyData = $exportData->getData('day');

// 2. 按周分组
$weeklyData = $exportData->getData('week');

// 3. 按月分组
$monthlyData = $exportData->getData('month');
```

## 返回数据结构

每种分组方式返回的数据结构相同，只是 `date` 字段的格式不同：

```php
[
    [
        'date' => '2024-01-15',  // 根据分组类型变化
        'score' => 85.5,         // 性能评分
        'fps' => 58.2,           // FPS均值
        'stutter' => 2.1,        // 卡顿率(%)
        'memory' => 512.3,       // 内存峰值均值(MB)
        'fps_jitter' => 1.2,     // FPS抖动
        'big_jank' => 0.8,       // BigJank卡顿
        'lua_memory' => 45.6,    // Lua内存均值(MB)
        'mono_used_size' => 78.9, // Mono内存均值(MB)
        'cpu_total' => 35.2,     // CPU使用峰值均值(%)
        'fps_power' => 12.5      // FpsPower均值(mw/帧)
    ],
    // ... 更多数据
]
```

## SQL 实现细节

### 按天分组
```sql
SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as date, ...
GROUP BY DATE_FORMAT(created_at, '%Y-%m-%d')
```

### 按周分组
```sql
SELECT CONCAT(
    DATE_FORMAT(DATE_SUB(created_at, INTERVAL WEEKDAY(created_at) DAY), '%Y-%m-%d'),
    '-',
    DATE_FORMAT(DATE_ADD(DATE_SUB(created_at, INTERVAL WEEKDAY(created_at) DAY), INTERVAL 6 DAY), '%Y-%m-%d')
) as date, ...
GROUP BY YEAR(created_at), WEEK(created_at, 1)
```

### 按月分组
```sql
SELECT DATE_FORMAT(created_at, '%Y-%m') as date, ...
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
```

## 注意事项

1. **周的计算**：按照 ISO 8601 标准，周一为一周的开始
2. **时区**：使用数据库服务器的时区设置
3. **性能**：不同分组方式的查询性能可能有差异，建议根据数据量选择合适的分组方式
4. **兼容性**：保持向后兼容，不传参数时默认按天分组

## 扩展说明

如需添加新的分组方式，可在 `getDateSelectAndGroup()` 方法中添加新的 case 分支。

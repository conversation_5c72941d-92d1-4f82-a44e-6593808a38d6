<?php

/**
 * 测试 ExportData 分组功能
 * 
 * 使用示例：
 * php test_export_data_grouping.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// 模拟测试参数
$params = [
    'start_time' => '2024-01-01',
    'end_time' => '2024-01-31',
    'developer_app_id' => 123,
    'os_type' => 1, // Android
];

// 创建 ExportData 实例
$exportData = new \App\Service\Apm\Stat\ExportData($params);

echo "=== ExportData 分组功能测试 ===\n\n";

// 测试不同的分组类型
$groupTypes = ['day', 'week', 'month'];

foreach ($groupTypes as $groupType) {
    echo "测试分组类型: {$groupType}\n";
    echo "调用方法: \$exportData->getData('{$groupType}')\n";

    try {
        // 这里只是展示调用方式，实际运行需要数据库连接
        echo "预期的时间格式:\n";
        switch ($groupType) {
            case 'day':
                echo "  示例: 2024-01-15\n";
                break;
            case 'week':
                echo "  示例: 2024-01-15-2024-01-21\n";
                break;
            case 'month':
                echo "  示例: 2024-01\n";
                break;
        }

        // $data = $exportData->getData($groupType);
        // echo "返回数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";

    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
    }

    echo str_repeat("-", 50) . "\n\n";
}

echo "=== 测试完成 ===\n";
echo "\n使用说明:\n";
echo "1. 按天分组: \$exportData->getData('day') 或 \$exportData->getData()\n";
echo "2. 按周分组: \$exportData->getData('week')\n";
echo "3. 按月分组: \$exportData->getData('month')\n";
echo "\n返回的时间格式:\n";
echo "- 按天: 2024-01-15\n";
echo "- 按周: 2024-01-15-2024-01-21 (周一到周日的日期范围)\n";
echo "- 按月: 2024-01\n";
